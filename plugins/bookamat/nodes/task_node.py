"""Bookamat task execution node."""

import logging
import time
from langchain_core.messages import HumanMessage
from langchain_openai import ChatOpenAI

from ..state import BookamatState
from ..tools import BOOKAMAT_TOOLS
from config.settings import settings

logger = logging.getLogger(__name__)


def bookamat_task_node(state: BookamatState) -> BookamatState:
    """
    Führt die nächste Aufgabe im Bookamat-Plan aus.

    Args:
        state: Aktueller BookamatState

    Returns:
        Aktualisierter BookamatState mit Aufgaben-Ergebnissen
    """
    try:
        start_time = time.time()

        # Get current plan and task
        plan = state.get("plan")
        if not plan or not plan.tasks:
            return {
                **state,
                "error": "Kein Ausführungsplan verfügbar",
                "current_step": state.get("current_step", 0) + 1
            }

        current_task_index = plan.current_task_index
        if current_task_index >= len(plan.tasks):
            return {
                **state,
                "error": "Alle Aufgaben bereits abgeschlossen",
                "current_step": state.get("current_step", 0) + 1
            }

        current_task = plan.tasks[current_task_index]
        current_step = state.get("current_step", 0)

        logger.info(f"Executing Bookamat task {current_task.id}: {current_task.description}")

        # Create LLM with tools
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=0.1,
            max_tokens=settings.agent_max_tokens,
            openai_api_key=settings.openai_api_key
        )

        llm_with_tools = llm.bind_tools(BOOKAMAT_TOOLS)

        # Get current messages
        messages = state.get("messages", [])

        # Create task message
        task_message = HumanMessage(content=f"""
Führe folgende Aufgabe aus:

Aufgabe {current_task.id}: {current_task.description}
Erwartetes Ergebnis: {current_task.deliverable}

Verwende die verfügbaren Bookamat-Tools, um diese Aufgabe zu erfüllen.
Antworte auf Deutsch.
        """)

        # Execute task with tools
        response = llm_with_tools.invoke(messages + [task_message])

        processing_time = time.time() - start_time

        # Mark current task as completed and move to next
        plan.tasks[current_task_index].completed = True
        plan.current_task_index += 1

        # Store the action result
        action_results = state.get("action_results", [])
        action_results.append({
            "task_id": current_task.id,
            "task_description": current_task.description,
            "response": response.content if response.content else "Tool-Aufruf ausgeführt",
            "processing_time": processing_time
        })

        logger.info(f"Completed Bookamat task {current_task.id}: {current_task.description}")

        return {
            **state,
            "plan": plan,
            "action_results": action_results,
            "current_step": current_step + 1,
            "model_used": settings.agent_model,
            "processing_time": processing_time,
            "messages": [response],  # Add response for tool processing
            "needs_action": plan.current_task_index < len(plan.tasks)  # Continue if more tasks
        }

    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"Fehler bei der Bookamat Aufgabenausführung: {str(e)}"
        logger.error(error_msg)

        return {
            **state,
            "error": error_msg,
            "processing_time": processing_time,
            "current_step": state.get("current_step", 0) + 1
        }
