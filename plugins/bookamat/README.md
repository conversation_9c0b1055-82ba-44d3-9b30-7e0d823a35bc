# Bookamat Plugin für LangGraph Agent

Dieses Plugin integriert die Bookamat Buchhaltungssoftware in den LangGraph Agent. Es ermöglicht dem Agenten, Buchungen zu erstellen und andere Buchhaltungsoperationen durchzuführen.

## Über Bookamat

Bookamat ist eine österreichische Buchhaltungssoftware, die als SaaS (Software as a Service) läuft. Sie bietet eine REST-API für den Zugriff auf und die Manipulation von Daten in einem Benutzerkonto.

Die API-Dokumentation finden Sie hier: [API DOCS](https://www.bookamat.com/dokumentation/api/v1/index.html)

## Features

### Verfügbare Tools

1. **`buchung_hinzufuegen`** - Fügt eine neue Buchung in Bookamat hinzu
   - Erstellt Einnahmen oder Ausgaben
   - Unterstützt verschiedene Steuerkonten und Umsatzsteuerkonten
   - Automatische Berechnung von Netto- und Steuerbeträgen

2. **`bookamat_konten_abrufen`** - Ruft verfügbare Konten ab
   - Zahlungsmittelkonten (Bankkonten)
   - Steuerkonten
   - Umsatzsteuerkonten
   - Kostenstellen

3. **`bookamat_verbindung_testen`** - Testet die API-Verbindung
   - Überprüft Authentifizierung
   - Validiert Konfiguration

### Subgraph-Architektur

Das Plugin implementiert einen eigenständigen Subgraph mit folgenden Komponenten:

- **Analysis Node**: Analysiert Benutzeranfragen und bestimmt erforderliche Aktionen
- **Execution Node**: Führt Bookamat-spezifische Operationen aus
- **Response Node**: Formatiert Antworten in deutscher Sprache
- **Tool Integration**: Nahtlose Integration in das Haupt-Tool-System

## Installation und Konfiguration

### 1. Umgebungsvariablen

Erstellen Sie eine `.env` Datei oder setzen Sie folgende Umgebungsvariablen:

```bash
# Bookamat API Zugangsdaten
BOOKAMAT_USERNAME=ihr_benutzername
BOOKAMAT_API_KEY=ihr_api_key

# Optional: Land und Jahr (Standard: "at" und aktuelles Jahr)
BOOKAMAT_COUNTRY=at
BOOKAMAT_YEAR=2024

# Buchhaltungssoftware-Auswahl (für Subgraph-Routing)
ACCOUNTING_SOFTWARE=bookamat
```

### 2. API-Key erhalten

1. Melden Sie sich bei Bookamat an
2. Gehen Sie zu **Mein Account**
3. Kopieren Sie Ihren persönlichen API-Key

### 3. Konfiguration testen

```python
from plugins.bookamat.tools import bookamat_verbindung_testen

# Test der Verbindung
result = bookamat_verbindung_testen()
print(result)
```

## Verwendung

### Direkte Tool-Verwendung

```python
from plugins.bookamat.tools import buchung_hinzufuegen

# Neue Buchung erstellen
result = buchung_hinzufuegen(
    title="Büroausstattung",
    amount="1200.00",
    bankaccount_id=1254,
    costaccount_id=2369,
    purchasetaxaccount_id=3476,
    tax_percent="20.00",
    date="2024-01-15",
    description="Kauf von Büromöbeln"
)
print(result)
```

### Über den Hauptagenten

Der Agent kann natürlichsprachliche Anfragen verarbeiten:

```
"Erstelle eine Buchung für Büroausstattung im Wert von 1200 Euro"
"Liste alle verfügbaren Bankkonten auf"
"Teste die Verbindung zu Bookamat"
```

### Subgraph direkt verwenden

```python
from plugins.bookamat.graph import run_bookamat_subgraph

result = run_bookamat_subgraph(
    "Erstelle eine Buchung für Serverkosten",
    thread_id="buchung_001"
)
print(result['output'])
```

## API-Referenz

### Buchung hinzufügen

**Erforderliche Parameter:**
- `title`: Buchungstitel (max. 50 Zeichen)
- `amount`: Bruttobetrag als String (z.B. "1200.00")
- `bankaccount_id`: ID des Zahlungsmittelkontos
- `costaccount_id`: ID des Steuerkontos
- `purchasetaxaccount_id`: ID des Umsatzsteuerkontos

**Optionale Parameter:**
- `tax_percent`: Umsatzsteuer in % (Standard: "20.00")
- `date`: Buchungsdatum (YYYY-MM-DD, ohne = offene Buchung)
- `date_invoice`: Rechnungsdatum (YYYY-MM-DD)
- `description`: Beschreibung (max. 500 Zeichen)
- `costcentre_id`: ID der Kostenstelle

### Beispiel-Response

```
✅ Buchung erfolgreich erstellt!

📋 Details:
• Buchungs-ID: 6784
• Belegnummer: 1-3
• Titel: Büroausstattung
• Betrag: 1200.00 €
• Status: gebucht
• Steuersatz: 20.00%
• Buchungsdatum: 2024-01-15
```

## Fehlerbehandlung

Das Plugin behandelt verschiedene Fehlertypen:

- **Konfigurationsfehler**: Fehlende API-Zugangsdaten
- **API-Fehler**: Ungültige Requests oder Netzwerkprobleme
- **Validierungsfehler**: Ungültige Eingabedaten
- **Authentifizierungsfehler**: Ungültige Zugangsdaten

Alle Fehlermeldungen werden in deutscher Sprache ausgegeben.

## Entwicklung

### Projektstruktur

```
plugins/bookamat/
├── __init__.py          # Package-Initialisierung
├── client.py            # Bookamat API Client
├── graph.py             # Subgraph-Definition
├── nodes.py             # Graph-Knoten
├── state.py             # State-Management
├── tools.py             # LangChain Tools
└── README.md            # Diese Dokumentation
```

### Tests ausführen

```bash
python test_bookamat_integration.py
```

### Neue Features hinzufügen

1. Erweitern Sie `client.py` für neue API-Endpunkte
2. Fügen Sie neue Tools in `tools.py` hinzu
3. Aktualisieren Sie `BOOKAMAT_TOOLS` Liste
4. Testen Sie die Integration

## Sicherheit

- API-Keys werden sicher in Umgebungsvariablen gespeichert
- Alle API-Aufrufe verwenden HTTPS
- Eingabedaten werden validiert
- Fehlerbehandlung verhindert Informationslecks

## Support

Bei Problemen oder Fragen:

1. Überprüfen Sie die Bookamat API-Dokumentation
2. Testen Sie die Verbindung mit `bookamat_verbindung_testen`
3. Prüfen Sie die Logs auf detaillierte Fehlermeldungen
4. Stellen Sie sicher, dass alle Umgebungsvariablen korrekt gesetzt sind

## Lizenz

Dieses Plugin folgt der Lizenz des Hauptprojekts.