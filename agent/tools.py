"""Tool implementations for the LangGraph agent."""

import logging
import os
import base64
from typing import Optional
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage

from config.settings import settings
from agent.state import EmailMessage

from openai import OpenAI
client = OpenAI()

# Import accounting subgraph if configured
try:
    if settings.accounting_software == "bookamat":
        from plugins.bookamat.graph import run_bookamat_subgraph
        ACCOUNTING_AVAILABLE = True
    else:
        ACCOUNTING_AVAILABLE = False
        run_bookamat_subgraph = None
except ImportError:
    ACCOUNTING_AVAILABLE = False
    run_bookamat_subgraph = None

logger = logging.getLogger(__name__)


@tool
def create_email(to_email: str, subject: str, body: str, from_name: Optional[str] = None) -> str:
    """
    Create an email message that will be queued for sending.

    This tool creates an EmailMessage object instead of directly sending the email.
    The email will be added to the pending emails list and shown to the user for approval.

    Args:
        to_email: Recipient email address
        subject: Email subject
        body: Email body content
        from_name: Optional sender name (defaults to configured email)

    Returns:
        Confirmation message in German with email details
    """
    try:
        # Create email message object
        email_message = EmailMessage(
            to_email=to_email,
            subject=subject,
            body=body,
            from_name=from_name
        )

        # Return structured data that can be parsed by the node
        # We use a special format that the node can recognize and extract
        return f"EMAIL_CREATED:{email_message.model_dump_json()}"

    except Exception as e:
        error_msg = f"Fehler beim Erstellen der Email: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def get_file_attachment(attachment_id: str) -> str:
    """
    Retrieve information about a file attachment from an email.

    This tool allows the agent to access information about file attachments that were received via email.
    IT WILL NOT RETURN THE FILE CONTENT ITSELF.
    It returns information about the attachment including its path, so the agent can
    read or process the file content.

    Args:
        attachment_id: The unique ID of the attachment to retrieve

    Returns:
        Information about the attachment in German, including file path and metadata
    """
    try:
        # Initialize database connection (import here to avoid circular import)
        from email_agent.database import EmailDatabase
        database = EmailDatabase()

        # Get attachment metadata
        attachment = database.get_attachment_by_id(attachment_id)

        if not attachment:
            return f"Anhang mit ID '{attachment_id}' wurde nicht gefunden."

        # Check if file still exists
        file_path = attachment['file_path']
        if not os.path.exists(file_path):
            return f"Anhang-Datei '{attachment['filename']}' wurde nicht gefunden (Pfad: {file_path})."

        # Return attachment information
        file_size_mb = attachment['file_size'] / (1024 * 1024)

        return f"""Anhang gefunden:
- Dateiname: {attachment['filename']}
- Dateipfad: {file_path}
- Dateityp: {attachment['content_type']}
- Dateigröße: {file_size_mb:.2f} MB
- Klassifikation: {attachment['classification_reason']}

Die Datei kann über den angegebenen Pfad gelesen werden."""

    except Exception as e:
        error_msg = f"Fehler beim Abrufen des Anhangs: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def list_email_attachments(email_context: Optional[str] = None) -> str:
    """
    List all available email attachments that the agent can access.

    This tool shows all file attachments from processed emails that are classified
    as relevant (not logos or signatures).

    Args:
        email_context: Optional context to filter attachments (not implemented yet)

    Returns:
        List of available attachments with their IDs and basic information in German
    """
    try:
        # Initialize database connection (import here to avoid circular import)
        from email_agent.database import EmailDatabase
        database = EmailDatabase()

        # Get all relevant attachments using database method
        import sqlite3
        try:
            with sqlite3.connect(database.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT unique_id, filename, content_type, file_size, email_uid
                    FROM email_attachments
                    WHERE is_relevant = TRUE
                    ORDER BY created_at DESC
                    LIMIT 20
                """)

                rows = cursor.fetchall()

                if not rows:
                    return "Keine E-Mail-Anhänge verfügbar."

                attachment_list = ["Verfügbare E-Mail-Anhänge:"]
                for row in rows:
                    unique_id, filename, content_type, file_size, email_uid = row
                    file_size_mb = file_size / (1024 * 1024)
                    attachment_list.append(
                        f"- ID: {unique_id} | {filename} ({content_type}, {file_size_mb:.2f} MB) [E-Mail: {email_uid}]"
                    )

                return "\n".join(attachment_list)
        except Exception as db_error:
            logger.error(f"Database query failed: {db_error}")
            return f"Fehler beim Datenbankzugriff: {str(db_error)}"

    except Exception as e:
        error_msg = f"Fehler beim Auflisten der Anhänge: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def process_file_with_llm(attachment_id: str, prompt: str) -> str:
    """
    Process a file attachment (image or PDF) with a custom prompt.

    This tool can process the content of a file attachment.
    It supports images (PNG, JPEG, GIF, WebP) and PDF files.

    Args:
        attachment_id: The unique ID of the attachment to process
        prompt: The prompt/question to send to the LLM along with the file. It must be self-contained with all the need context.

    Returns:
        The response to the prompt about the file content in German
    """
    try:
        # Initialize database connection
        from email_agent.database import EmailDatabase
        database = EmailDatabase()

        # Get attachment information
        import sqlite3
        with sqlite3.connect(database.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT filename, content_type, file_path, file_size
                FROM email_attachments
                WHERE unique_id = ? AND is_relevant = TRUE
            """, (attachment_id,))

            result = cursor.fetchone()
            if not result:
                return f"Anhang mit ID '{attachment_id}' nicht gefunden oder nicht relevant."

            filename, content_type, file_path, file_size = result

        # Check if file exists
        if not os.path.exists(file_path):
            return f"Datei nicht gefunden: {file_path}"

        # Check file type and size
        max_size_mb = 20  # OpenAI limit for vision
        file_size_mb = file_size / (1024 * 1024)

        if file_size_mb > max_size_mb:
            return f"Datei zu groß ({file_size_mb:.2f} MB). Maximum: {max_size_mb} MB."

        # Initialize OpenAI client
        llm = ChatOpenAI(
            model="gpt-4o",  # Use GPT-4 Vision for file processing
            temperature=0.7,
            max_tokens=1500,
            openai_api_key=settings.openai_api_key
        )

        # Process based on file type
        if content_type.startswith('image/'):
            return _process_image_with_openai(file_path, filename, prompt, llm)
        elif content_type == 'application/pdf':
            return _process_pdf_with_openai(file_path, filename, prompt, llm)
        else:
            return f"Dateityp '{content_type}' wird nicht unterstützt. Unterstützte Typen: Bilder (PNG, JPEG, GIF, WebP) und PDF-Dateien."

    except Exception as e:
        error_msg = f"Fehler beim Verarbeiten der Datei: {str(e)}"
        logger.error(error_msg)
        return error_msg


def _process_image_with_openai(file_path: str, filename: str, prompt: str, llm: ChatOpenAI) -> str:
    """Process an image file with OpenAI Vision API."""
    try:
        # Read and encode image
        with open(file_path, "rb") as image_file:
            image_data = base64.b64encode(image_file.read()).decode('utf-8')

        # Determine image format
        file_extension = os.path.splitext(filename)[1].lower()
        if file_extension == '.jpg' or file_extension == '.jpeg':
            image_format = 'jpeg'
        elif file_extension == '.png':
            image_format = 'png'
        elif file_extension == '.gif':
            image_format = 'gif'
        elif file_extension == '.webp':
            image_format = 'webp'
        else:
            image_format = 'jpeg'  # Default fallback

        # Create message with image
        system_message = SystemMessage(content="""Du bist ein hilfreicher Assistent, der Bilder analysiert und auf Deutsch antwortet.
Analysiere das bereitgestellte Bild sorgfältig und beantworte die gestellte Frage präzise und detailliert.""")

        # Create human message with image
        human_message = HumanMessage(
            content=[
                {
                    "type": "text",
                    "text": f"Dateiname: {filename}\n\nFrage/Aufgabe: {prompt}"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/{image_format};base64,{image_data}"
                    }
                }
            ]
        )

        # Get response from OpenAI
        response = llm.invoke([system_message, human_message])

        return f"Analyse von '{filename}':\n\n{response.content}"

    except Exception as e:
        logger.error(f"Error processing image {filename}: {e}")
        return f"Fehler beim Verarbeiten des Bildes '{filename}': {str(e)}"


def _process_pdf_with_openai(file_path: str, filename: str, prompt: str, llm: ChatOpenAI) -> str:
    """Process a PDF file with OpenAI API."""

    file = client.files.create(
        file=open(file_path, "rb"),
        purpose="user_data"
    )

    completion = client.chat.completions.create(
        model="gpt-4.1",
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "file",
                        "file": {
                            "file_id": file.id,
                        }
                    },
                    {
                        "type": "text",
                        "text": prompt
                    },
                ]
            }
        ]
    )

    return completion.choices[0].message.content


@tool
def accounting(prompt: str, files: Optional[str] = None) -> str:
    """
    Führt Buchhaltungsoperationen über die konfigurierte Buchhaltungssoftware aus.

    Dieses Tool leitet Anfragen an die konfigurierte Buchhaltungssoftware weiter (z.B. Bookamat).
    Es kann Buchungen erstellen, Konten abrufen und andere buchhaltungsrelevante Aufgaben ausführen.

    Args:
        prompt: Detaillierte Beschreibung der gewünschten Buchhaltungsoperation
        files: Optional - Referenz zu relevanten Dateien (z.B. Rechnungen, Belege)

    Returns:
        Ergebnis der Buchhaltungsoperation in deutscher Sprache
    """
    try:
        if not ACCOUNTING_AVAILABLE:
            return f"❌ Buchhaltungssoftware '{settings.accounting_software}' ist nicht verfügbar oder nicht konfiguriert."

        # Prepare the full prompt with file information if provided
        full_prompt = prompt
        if files:
            full_prompt += f"\n\nRelevante Dateien: {files}"

        # Route to the appropriate accounting subgraph
        if settings.accounting_software == "bookamat":
            result = run_bookamat_subgraph(full_prompt)

            if result['success']:
                return result['output']
            else:
                error_msg = result.get('error', 'Unbekannter Fehler')
                return f"❌ Fehler bei der Buchhaltungsoperation: {error_msg}"
        else:
            return f"❌ Buchhaltungssoftware '{settings.accounting_software}' wird noch nicht unterstützt."

    except Exception as e:
        error_msg = f"Fehler beim Ausführen der Buchhaltungsoperation: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# List of available tools for the agent
TOOLS = [create_email, get_file_attachment, list_email_attachments, process_file_with_llm, accounting]
