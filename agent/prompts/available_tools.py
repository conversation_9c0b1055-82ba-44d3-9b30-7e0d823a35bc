available_tools = """
#### create_email
Create an email message that will be queued for sending.

This tool creates an EmailMessage object instead of directly sending the email.
The email will be added to the pending emails list and shown to the user for approval.

#### process_file_with_llm
Process a file attachment (image or PDF) with a custom prompt.

This tool can process the content of a file attachment.
It supports images (PNG, JPEG, GIF, WebP) and PDF files.

#### list_email_attachments
List all available email attachments that the agent can access.

This tool shows all file attachments from processed emails that are classified
as relevant (not logos or signatures).

#### process_file_with_openai
Process a file attachment (image or PDF) with OpenAI API using a custom prompt.

This tool takes a file attachment and sends it to OpenAI with a custom prompt
for analysis. It supports images (PNG, JPEG, GIF, WebP) and PDF files.
The response from OpenAI is returned and can be added to the conversation.
"""